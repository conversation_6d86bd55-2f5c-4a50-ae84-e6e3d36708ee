"""Utility functions for button layout in the UI."""

# Button layout configuration - modified to display one product per line
# All products are now displayed on separate lines for better readability and organization
NAME_LENGTH_THRESHOLD = 20  # Legacy threshold - no longer used but kept for potential future use


def should_use_single_row(text: str) -> bool:
    """
    Determine if a button should be displayed in its own row.

    Modified to always return True, ensuring each product is displayed on its own
    separate line for better readability and organization. This provides a cleaner,
    more organized layout where each product gets its own dedicated row.

    Usage in button creation:
        button = InlineKeyboardButton(text=name, callback_data="some_data")

        if should_use_single_row(name):
            inline_keyboard.append([button])  # Each product gets its own row
        else:
            buttons_collection.append(button)  # This path is no longer used
            if len(buttons_collection) == 2:  # Arrange 2 buttons per row
                inline_keyboard.append(buttons_collection)
                buttons_collection = []

    Args:
        text: The text to be displayed on the button

    Returns:
        bool: Always True - each product should get its own row for better organization
    """
    # Always return True to ensure each product is displayed on its own line
    # This provides better readability and organization compared to the previous
    # two-products-per-line layout
    return True
